import { brBuilder, createContainer, createRow, createSeparator, createTextDisplay } from "@magicyan/discord";
import { ButtonBuilder, ButtonStyle, InteractionReplyOptions } from "discord.js";
import { icon } from "functions/utils/emojis.js";

export function settingsMainMenu() {
    const container = createContainer({
        accentColor: constants.colors.default,
        components: [
            createTextDisplay(brBuilder(
                `${icon.settings} Configurações`,
                "- Definir canais",
                "- Definir categorias de ticket",
                "- Definir cargos de tickets",
            )),
            createSeparator(true),
            createRow(
                new ButtonBuilder({
                    customId: "settings/channels",
                    label: "Canais",
                    emoji: icon.info,
                    style: ButtonStyle.Secondary,
                }),
                new ButtonBuilder({
                    customId: "settings/parents",
                    label: "Categorias",
                    emoji: icon.info,
                    style: ButtonStyle.Secondary,
                }),
                new ButtonBuilder({
                    customId: "settings/roles",
                    label: "Cargos",
                    emoji: icon.info,
                    style: ButtonStyle.Secondary,
                }),
            )
        ]
    });
    // return { ephemeral: true, embeds: [embed], components: [row] }
    return { flags: ["Ephemeral", "IsComponentsV2"], components: [container] } satisfies InteractionReplyOptions;
}